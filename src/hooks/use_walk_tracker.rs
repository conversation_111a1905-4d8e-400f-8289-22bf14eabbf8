use dioxus::{
    prelude::*,
    signals::Signal,
};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Debug)]
pub struct WalkTracker {
    pub is_walking: bool,
    pub start_time: Option<u64>, // Using u64 for simplicity, represents timestamp
    pub duration:   u64,         // Duration in seconds
}

impl WalkTracker {
    pub fn new() -> Self {
        Self {
            is_walking: false,
            start_time: None,
            duration:   0,
        }
    }
}

pub fn use_walk_tracker() -> Signal<WalkTracker> { Signal::new(WalkTracker::new()) }
