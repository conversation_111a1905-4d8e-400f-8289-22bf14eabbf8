use chrono::{
    DateTime,
    Duration,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

#[derive(<PERSON><PERSON>, PartialEq, Debug, Deserialize, Serialize)]
pub enum ActivityType {
    Walking,
    Running,
    Mixed,
}

#[derive(<PERSON><PERSON>, PartialEq, Debug, Deserialize, Serialize)]
pub struct TypicalActivity {
    pub duration:      u32,
    pub activity_type: Option<String>, // "walking", "running", "mixed"
}

#[derive(<PERSON>lone, PartialEq, Debug, Deserialize, Serialize)]
pub struct Activity {
    // pub id:            String,
    pub activity_type: ActivityType,
    pub start_time:    DateTime<Utc>,
    pub end_time:      Option<DateTime<Utc>>,
}

impl Activity {
    pub fn duration(&self) -> Duration {
        match self.end_time {
            Some(end_time) => end_time - self.start_time,
            None => chrono::offset::Utc::now() - self.start_time,
        }
    }
}
