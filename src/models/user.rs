use serde::{
    Deserialize,
    Serialize,
};


#[derive(<PERSON><PERSON>, <PERSON>ialEq, Debug, Deserialize, Serialize)]
pub struct UserProfile {
    pub anonymous:             bool,
    pub authenticated:         bool,
    pub email:                 Option<String>,
    pub provider:              Option<String>, // "google", "apple", "email"
    pub email_confirmed_at:    Option<String>, // Using String for simplicity
    pub notifications_enabled: bool,
    pub unit_system:           String, // "metric" or "imperial"
}

impl Default for UserProfile {
    fn default() -> Self {
        Self {
            anonymous:             true,
            authenticated:         false,
            email:                 None,
            provider:              None,
            email_confirmed_at:    None,
            notifications_enabled: false,
            unit_system:           "metric".to_string(),
        }
    }
}
