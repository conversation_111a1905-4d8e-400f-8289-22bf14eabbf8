use dioxus::{
    logger::tracing::debug,
    prelude::*,
};

use crate::{
    i18n::*,
    models::*,
};


const HEADER_PT: &str = if cfg!(any(target_os = "ios", target_os = "android")) {
    "pt-8"
} else {
    "pt-0"
};
const HEADER_MT: &str = if cfg!(any(target_os = "ios")) {
    "-mt-40"
} else {
    "-mt-1"
};


#[derive(PartialEq, Props, Clone)]
pub struct AvatarProps {
    pub name:    String,
    #[props(optional)]
    pub src:     Option<String>,
    #[props(default = "md".to_string())]
    pub size:    String,
    #[props(optional)]
    pub onclick: EventHandler<MouseData>,
}

#[component]
fn Avatar(props: AvatarProps) -> Element {
    let initials = props
        .name
        .split(' ')
        .map(|part| part.chars().next().unwrap_or(' ').to_string())
        .collect::<String>()
        .to_uppercase();

    rsx! {
        div { class: "h-12 w-12 rounded-full shadow-md hover:shadow-lg transition-shadow duration-200",
            div { class: "h-full w-full flex items-center justify-center pet-gradient text-white",
                {initials}
            }
        }
    }
}


#[derive(PartialEq, Props, Clone)]
pub struct PageHeaderProfileProps {
    pub dog:  Dog,
    pub dogs: Vec<Dog>,
}

#[component]
pub fn PageHeaderProfile(props: PageHeaderProfileProps) -> Element {
    let navigator = use_navigator();
    let state = use_state(AppEvent::DogSelected);
    let dog = use_memo(move || state.read().get_selected_dog().unwrap().clone());

    let handle_pet_select = {
        move |dog_id: String| {
            use_context::<AppState>().set_selected_dog_id(&dog_id);
        }
    };

    debug!("PageHeaderProfile: pt = {}, mt = {}", HEADER_PT, HEADER_MT);

    rsx! {
        div { class: "page-header {HEADER_MT} {HEADER_PT}",
            // style: "padding-top: env(safe-area-inset-top, 0);",
            div { class: "flex justify-between items-start mt-2",
                div { class: "flex items-start gap-0",
                    div { class: "text-white pt-2",
                        p { class: "text-black/80 text-sm uppercase",
                            {props.dog.breed.as_deref().unwrap_or("Unknown breed")}
                            " • "
                            {dog().get_age()}
                        }
                        // DropdownMenu equivalent
                        div {
                            button {
                                class: "text-white flex gap-0 items-center mt-3 mb-2 h-4 p-0",
                                onclick: move |_| {},
                                div { class: "flex justify-center",
                                    h1 { class: "text-black/80 text-2xl font-bold",
                                        {props.dog.name.clone()}
                                    }
                                    // Placeholder for ChevronDown icon
                                    div { class: "text-black/80 h-8 w-4 size-8 mt-2",
                                        "▼"
                                    }
                                }
                            }
                            // DropdownMenuContent equivalent
                            div { class: "bg-white ml-3 min-w-64 max-w-72 pt-2 pb-3",
                                for dog in props.dogs.iter().cloned() {
                                    button {
                                        key: "{dog.id.clone()}",
                                        class: "flex gap-2 items-center cursor-pointer",
                                        onclick: move |_| {
                                            handle_pet_select(dog.id.clone());
                                        },
                                        Avatar {
                                            name: dog.name.clone(),
                                            src: dog.image.clone(),
                                            size: "xs".to_string(),
                                        }
                                        span { {dog.name} }
                                    }
                                }
                                hr { class: "my-2" }
                                button {
                                    class: "mt-2 pt-1 cursor-pointer",
                                    onclick: move |_| {
                                        navigator.push("/add-dog");
                                    },
                                    "+ "
                                    {t!("common-add-another-dog")}
                                }
                            }
                        }
                    }
                }
                Avatar {
                    name: props.dog.name.clone(),
                    src: props.dog.image.clone(),
                    size: "sm".to_string(),
                    onclick: move |_| {
                        navigator.push(format!("/dog/{}", props.dog.id));
                    },
                }
            }
        }
    }
}

#[derive(PartialEq, Props, Clone)]
pub struct PageHeaderProps {
    pub title:            String,
    #[props(default = true)]
    pub show_back_button: bool,
    #[props(default = "/".to_string())]
    pub back_path:        String,
    #[props(optional)]
    pub gradient_class:   Option<String>,
    #[props(optional)]
    pub children:         Element,
}

#[component]
pub fn PageHeader(props: PageHeaderProps) -> Element {
    // let state = use_context::<AppState>();
    let navigator: Navigator = use_navigator();

    // let gradient_class = use_memo(move || {
    //     if props.gradient_class.is_some() {
    //         return props.gradient_class.clone().unwrap();
    //     }
    //     match state.theme {
    //         Theme::Light => "bg-linear-to-b from-pet-purple to-pet-darkPurple".to_string(),
    //         Theme::Dark => "bg-linear-to-b from-pet-darkPurple to-pet-navy".to_string(),
    //         Theme::Colored => "bg-transparent".to_string(),
    //     }
    // });

    rsx! {
        div { class: "page-header {HEADER_MT} {HEADER_PT}",
            div { class: "flex items-center justify-between mt-3",
                div { class: "flex items-center",
                    if props.show_back_button {
                        button {
                            class: "rounded-full p-2 bg-black hover:bg-black/90 transition-colors",
                            onclick: move |_| {
                                navigator.push(props.back_path.as_str());
                            },
                            // Placeholder for ArrowLeft icon
                            div { class: "h-5 w-5", "⬅️" }
                        }
                    }
                    h1 { class: "text-xl font-bold",
                        // class: {if props.show_back_button "ml-3" else ""},
                        {props.title}
                    }
                }
                {props.children}
            }
        }
    }
}
