use dioxus::{
    logger::tracing::{
        debug,
        info,
    },
    prelude::*,
};
use dioxus_free_icons::{
    IconShape,
    icons::{
        fa_solid_icons::FaDog,
        ld_icons::{
            LdDog,
            LdHome,
            LdPlay,
            LdSettings,
            LdSquare,
        },
    },
};

use crate::{
    models::*,
    ui::{
        app::Route,
        components::base::{
            Button,
            ButtonVariant,
            Icon,
        },
        layout::Col,
    },
    utils::misc::concat_classes,
};


struct NavbarItem {
    icon:        Option<&'static dyn IconShape>,
    label:       &'static str,
    path:        &'static str,
    color_class: Option<&'static str>,
}

const NAV_ITEMS: [NavbarItem; 5] = [
    NavbarItem {
        icon:        Some(&LdHome),
        label:       "Home",
        path:        "/",
        color_class: Some("icon-activity"),
    },
    NavbarItem {
        icon:        Some(&LdDog),
        label:       "Profile",
        path:        "/dog",
        color_class: Some("icon-dog"),
    },
    NavbarItem {
        icon:        None,
        label:       "",
        path:        "",
        color_class: None,
    },
    NavbarItem {
        icon:        Some(&FaDog),
        label:       "AI",
        path:        "/ai",
        color_class: Some("icon-food"),
    },
    NavbarItem {
        icon:        Some(&LdSettings),
        label:       "Settings",
        path:        "/settings",
        color_class: Some("icon-weight"),
    },
    // NavbarItem {
    //     icon:        Some(LD_SETTINGS),
    //     label:       "Onboarding",
    //     path:        "/onboarding",
    //     color_class: Some("icon-weight"),
    // },
];


#[component]
pub fn BottomNavbar() -> Element {
    // let is_walking = use_context::<AppState>().is_walking;
    let theme = use_context::<AppState>().theme;
    let current_path = use_route::<Route>().to_string();

    let get_icon_color = |is_active: bool| {
        if theme == Theme::Colored {
            if is_active { "text-white" } else { "text-gray-500" }
        } else if is_active {
            // "text-black"
            "text-secondary-foreground"
        } else {
            // "text-gray-400"
            "text-gray-800"
        }
    };

    let is_active = |item: &NavbarItem| current_path == item.path;

    rsx! {
        Fragment {
            // if is_walking() {
            //   WalkStatusBar {}
            // }

            div {
                class: "fixed bottom-2 left-5 right-5 px-1 py-1 flex justify-around items-end z-10 transition-colors duration-300 backdrop-blur-sm rounded-4xl shadow-md",
                // class: if theme == Theme::Light { "bg-background/35" } else if theme == Theme::Dark { "bg-card" } else { "bg-white/5" },
                class: if theme == Theme::Light { "bg-white/55" } else if theme == Theme::Dark { "bg-card" } else { "bg-white/5" },
                for (i , item) in NAV_ITEMS.iter().enumerate() {
                    // let is_active = current_path == item.path;
                    if item.icon.is_some() {
                        Link {
                            key: "{i}",
                            to: "{item.path}",
                            class: concat_classes(
                                &[
                                    "flex flex-col items-center justify-center px-4 pt-1 w-1/5",
                                    get_icon_color(is_active(item)),
                                    if is_active(item) { "bg-background-darker/40 rounded-4xl" } else { "" },
                                    if theme == Theme::Colored { "hover:bg-white/10" } else { "" },
                                ],
                            ),
                            div { class: if is_active(item) { "animate-bounce-light" } else { "" },
                                // class: if theme == Theme::Colored && is_active(item) { item.color_class.as_deref().unwrap_or("") } else { "" },
                                Icon { size: 24, icon: item.icon.unwrap() }
                            }
                            span { class: "text-xs mt-0.5", "{item.label}" }
                        }
                    } else {
                        div { class: "w-24 relative", key: "{i}", WalkButton {} }
                    }
                }
            }
        }
    }
}

#[component]
fn WalkButton() -> Element {
    info!("WalkButton()");
    // let navigator = use_navigator();
    let current_path = use_route::<Route>().to_string();
    let mut state = use_state(AppEvent::ActivityStateUpdated);
    let is_active = state.read().get_selected_dog().unwrap().is_active();

    rsx! {
        Col {
            // class: "absolute bottom-3",
            class: "-mt-19",
            Button {
                class: concat_classes(
                    &[
                        "rounded-full flex items-center justify-center shadow-lg",
                        if is_active {
                            "bg-pet-pink hover:bg-pet-pink/90"
                        } else {
                            "bg-secondary hover:bg-secondary/90"
                        },
                        if current_path == "/ai" || current_path == "/settings" {
                            "w-10 h-10"
                        } else {
                            "w-14 h-14"
                        },
                    ],
                ),
                onclick: move |_| {
                    state.apply(AppAction::ActivityToggle);
                },
                Icon {
                    class: "text-white",
                    size: 6,
                    icon: if is_active { &LdSquare } else { &LdPlay },
                }
            }
            div { class: "text-xs my-0.5 font-bold",
                if is_active {
                    ActivityTimer {}
                } else {
                    div { class: "text-xs mt-2 font-bold" }
                }
            }
        }
    }
}

#[component]
fn ActivityTimer() -> Element {
    info!("ActivityTimer()");
    let state = use_state(AppEvent::ActivityStateUpdated);
    let duration = use_memo(move || {
        state
            .read()
            .get_selected_dog()
            .unwrap()
            .get_current_activity_duration()
    });

    let format_duration = move || {
        let total_seconds = duration().num_seconds();
        let hours = total_seconds / 60 / 60;
        let minutes = total_seconds / 60;
        let seconds = total_seconds % 60;
        format!("{hours}:{minutes:02}:{seconds:02}")
    };

    rsx! {
        div { class: "text-xs mt-2 font-bold", "{format_duration()}" }
    }
}
