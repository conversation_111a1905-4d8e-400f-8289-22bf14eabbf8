use dioxus::prelude::*;

use crate::hooks::data::use_weight_log_data;

mod weight_history_table;
use weight_history_table::*;

#[component]
pub fn WeightHistory() -> Element {
    let weight_logs = use_weight_log_data();

    rsx! {
      div { class: "p-4",
        h1 { class: "text-2xl font-bold", "Weight History" }
        div { class: "mt-4",
          WeightHistoryTable { logs: weight_logs.read().clone() }
        }
      }
    }
}
