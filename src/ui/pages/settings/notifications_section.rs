use dioxus::prelude::*;

use crate::{
    models::state::*,
    ui::{
        components::base::{
            Row,
            Switch,
        },
        layout::{
            Card,
            Content,
            Description,
            Header,
            Section,
            Title,
        },
    },
};

#[component]
pub fn NotificationsSection() -> Element {
    let mut state = use_state(AppEvent::NotificationsToggle);

    rsx! {
      Section {
        Header {
          Title { "Notifications" }
          Description { "Manage your notification settings." }
        }
        Content {
          Row {
            span { "Enable Notifications" }
            Switch {
              checked: state.read().user.notifications_enabled,
              on_change: move |v| {
                  state
                      .apply(AppAction::NotificationsToggle {
                          enabled: v,
                      });
              },
            }
          }
        }
      }
    }
}
