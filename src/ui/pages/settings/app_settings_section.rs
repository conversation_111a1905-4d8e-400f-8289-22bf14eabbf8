use dioxus::prelude::*;

use crate::ui::{
    components::base::{
        Select,
        SelectContent,
        SelectItem,
        SelectTrigger,
        SelectValue,
    },
    layout::{
        Card,
        Content,
        Description,
        Header,
        Section,
        Title,
    },
};

#[derive(Props, PartialEq, Clone)]
pub struct AppSettingsSectionProps {
    pub unit_system:           String,
    #[props(default)]
    pub on_unit_system_change: EventHandler<String>,
}

#[component]
pub fn AppSettingsSection(props: AppSettingsSectionProps) -> Element {
    rsx! {
      Section {
        Header {
          Title { "App Settings" }
          Description { "Manage your application settings." }
        }
        Content {
          div { class: "flex items-center justify-between",
            span { "Unit System" }
            Select {
              SelectTrigger {
                SelectValue { placeholder: Some(props.unit_system.clone()) }
              }
              SelectContent {
                SelectItem { value: "metric".to_string(), "Metric" }
                SelectItem { value: "imperial".to_string(), "Imperial" }
              }
            }
          }
        }
      }
    }
}
