use dioxus::prelude::*;

use crate::{
    models::*,
    // ui::components::*,
};

mod activity_card;
use activity_card::*;

#[component]
pub fn ActivityHistoryPage() -> Element {
    let state = use_context::<AppState>();

    rsx!(
        div {
            "Activity History Page"
            ActivityCard {
                icon: "Activity".to_string(),
                title: "Walk".to_string(),
                time: "10:00 AM".to_string(),
                duration: "30 min".to_string(),
                calories: 150,
                color: "bg-pet-lightBlue".to_string(),
                class: "".to_string(),
            }
        }
    )
}
