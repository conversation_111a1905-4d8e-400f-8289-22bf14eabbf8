use dioxus::prelude::*;

// use rand::seq::SliceRandom;
use crate::{
    hooks::use_walk_tracker,
    i18n::*,
    models::*,
    ui::components::advanced::{
        MetricCard,
        WalkStatusBar,
    },
    ui::components::base::ProgressBar,
    ui::layout::PageHeaderProfile,
};
mod daily_diet_card;
use daily_diet_card::*;
mod dog_recommendation_card;
use dog_recommendation_card::*;
mod treat_tracker;
use treat_tracker::*;


// Sample recommendations (in a real app these would come from the backend)
fn get_recommendations() -> Vec<DogRecommendation> {
    vec![
        DogRecommendation {
            id:       "1".to_string(),
            text:     "Do not feed your dog before a walk. Wait at least 1 hour after feeding before any \
                       moderate to intensive exercise."
                .to_string(),
            seen:     false,
            category: "diet".to_string(),
        },
        DogRecommendation {
            id:       "2".to_string(),
            text:     "Do not feed from the table. Human food can contain ingredients that are harmful to \
                       dogs and can lead to obesity."
                .to_string(),
            seen:     false,
            category: "diet".to_string(),
        },
        DogRecommendation {
            id:       "3".to_string(),
            text:     "Provide constant access to fresh, clean water. This is especially important when \
                       increasing your dog's activity level."
                .to_string(),
            seen:     false,
            category: "health".to_string(),
        },
        DogRecommendation {
            id:       "4".to_string(),
            text:     "Remember to choose food for your dog considering allergies. If you notice signs of \
                       food allergies (scratching, digestive issues), consult your vet."
                .to_string(),
            seen:     false,
            category: "health".to_string(),
        },
        DogRecommendation {
            id:       "5".to_string(),
            text:     "Visit veterinary if your dog started to drink unusually little or, conversely, \
                       much more than usual. This could be a sign of underlying health issues."
                .to_string(),
            seen:     false,
            category: "health".to_string(),
        },
    ]
}

#[component]
pub fn Home() -> Element {
    // return rsx! { "No dog selected" };

    let state = use_context::<AppState>();
    let state2 = use_context::<AppState>();
    let dogs = use_memo(move || state2.dogs.clone());
    if dogs().is_empty() {
        return rsx! { "No dog selected" };
    }

    // * Redirect to onboarding if no dog profile is found
    use_effect(move || {
        // if state.dogs.is_empty() {
        if use_context::<AppState>().dogs.is_empty() {
            use_navigator().push("/onboarding");
        }
    });

    let dog = use_memo(move || state.get_selected_dog().unwrap().clone());
    let walk_tracker = use_walk_tracker();
    // let dog = use_memo(move || {
    //     use_context::<AppState>()
    //         .get_selected_dog()
    //         .unwrap()
    //         .clone()
    // });
    // let dogs = use_memo(move || use_context::<AppState>().dogs.clone());
    // let dogs = || {
    //     vec![Dog {
    //         id:                  "1".to_string(),
    //         name:                "Rex".to_string(),
    //         birthday:            Some("2020-01-01".to_string()),
    //         activity_progress:   Some(0),
    //         calorie_progress:    Some(0),
    //         body_fit_state:      Some(7),
    //         daily_calorie_goal:  Some(2000),
    //         daily_activity_goal: Some(30),
    //         breed:               Some("Labrador Retriever".to_string()),
    //         size:                Some("large".to_string()),
    //         weight:              Some(30),
    //         image:               Some(
    //             "https://images.dog.ceo/breeds/labrador/n02099712_315.jpg".to_string(),
    //         ),
    //         food_history:        vec![],
    //         preferred_foods:     vec![],
    //         last_weight_update:  Some("2020-01-01".to_string()),
    //         typical_activity:    Some(TypicalActivity {
    //             duration:      30,
    //             activity_type: Some("walking".to_string()),
    //         }),
    //         r#type:              Some("dog".to_string()),
    //     }]
    // };
    // let dog = || dogs().first().unwrap().clone();


    let recommendations = get_recommendations();
    let current_recommendation = use_signal(|| None::<DogRecommendation>);

    // Show recommendations effect
    use_effect(move || {
        let unseen_recommendations: Vec<DogRecommendation> = recommendations
            .iter()
            .filter(|rec| !rec.seen)
            .cloned()
            .collect();

        if !unseen_recommendations.is_empty() {
            // let mut rng = rand::thread_rng();
            // if let Some(random_rec) = unseen_recommendations.choose(&mut rng) {
            //     current_recommendation.set(Some(random_rec.clone()));
            // }
        }

        // * Check onboarding status and show toast
        // let has_completed_onboarding = get_onboarding_completed();
        // let has_shown_welcome_notification = get_welcome_notification_shown();

        // if !has_completed_onboarding && !has_shown_welcome_notification {
        //     // Simulate toast
        //     info!(
        //         "Welcome to My Dog In Fit! Complete your dog's profile in the Settings section to get \
        //          personalized recommendations."
        //     );
        //     // set_welcome_notification_shown(true);
        // }
    });

    let handle_mark_recommendation_as_seen = move |id: String| {
        // let updated_recs: Vec<DogRecommendation> = recommendations
        //     .iter()
        //     .map(|rec| {
        //         if rec.id == id {
        //             DogRecommendation {
        //                 seen: true,
        //                 ..rec.clone()
        //             }
        //         } else {
        //             rec.clone()
        //         }
        //     })
        //     .collect();
        // save_recommendations(&updated_recs);
        // recommendations.set(updated_recs);
        // current_recommendation.set(None);

        // * After a delay, show another recommendation if available
        // let recs_clone = recommendations.clone();
        // let current_rec_setter = current_recommendation.setter();
        // cx.spawn(async move {
        //     dioxus::tokio::time::sleep(Duration::from_secs(60)).await;
        //     let unseen_recommendations: Vec<DogRecommendation> = recs_clone
        //         .iter()
        //         .filter(|rec| !rec.seen && rec.id != id)
        //         .cloned()
        //         .collect();
        //     if !unseen_recommendations.is_empty() {
        //         let mut rng = rand::thread_rng();
        //         if let Some(random_rec) = unseen_recommendations.choose(&mut rng) {
        //             current_rec_setter(Some(random_rec.clone()));
        //         }
        //     }
        // });
    };

    let handle_add_treat = move || {
        // let treats_today = dog.treats_today.unwrap_or(0) + 1;
        // let diet_adjustment = (treats_today * 5).min(30); // Cap at 30%
        // dog.treats_today = Some(treats_today);
        // dog.diet_adjustment = Some(diet_adjustment as i32);
    };

    let handle_remove_treat = move || {
        // if dog.treats_today.unwrap_or(0) > 0 {
        //     let treats_today = dog.treats_today.unwrap_or(0) - 1;
        //     let diet_adjustment = (treats_today * 5).max(0);
        //     dog.treats_today = Some(treats_today);
        //     dog.diet_adjustment = Some(diet_adjustment as i32);
        // }
    };

    let get_diet_impact_text = move || -> String {
        // let adjustment = dog.diet_adjustment.unwrap_or(0);
        // if adjustment == 0 {
        t!("diet.none")
        // } else {
        //     format!("{:+}% {}", adjustment, t!("diet.calories"))
        // }
    };

    let activity_progress = dog().activity_progress.unwrap_or(0);
    let daily_activity_goal = dog().daily_activity_goal.unwrap_or(30);
    let calorie_progress = dog().calorie_progress.unwrap_or(0);
    let daily_calorie_goal = dog().daily_calorie_goal.unwrap_or(2000);

    return rsx! {
      div { class: "pb-20 pt-1",
        PageHeaderProfile { dog: dog().clone(), dogs: dogs() }

        div { class: "section-colored",
          div { class: "mb-4",
            WalkStatusBar { tracker: walk_tracker }
          }
          // * Health Metrics
          div { class: "mb-8",
            h3 { class: "text-lg font-bold mb-4", {t!("home-health-metrics")} }
            div { class: "grid grid-cols-2 gap-4",
              MetricCard {
                icon: "❤️".to_string(),
                title: "BCS".to_string(),
                value: dog().body_fit_state.unwrap_or(7).to_string(),
                unit: "".to_string(),
                color: "orange".to_string(),
              }
              Link { to: "/activities/history",
                MetricCard {
                  icon: "🏃".to_string(),
                  title: "BMI".to_string(),
                  value: dog().daily_activity_goal.unwrap_or(0).to_string(),
                  unit: "".to_string(),
                  color: "blue".to_string(),
                }
              }
              Link { to: "/weight-history",
                MetricCard {
                  icon: "⚖️".to_string(),
                  title: "Weight".to_string(),
                  value: dog().weight.unwrap_or(0).to_string(),
                  unit: "kg".to_string(),
                  color: "pink".to_string(),
                }
              }
              MetricCard {
                icon: "✅".to_string(),
                title: "Health Score".to_string(),
                value: "85".to_string(),
                unit: "%".to_string(),
                color: "yellow".to_string(),
              }
            }
          }

          // * Activity Summary
          div { class: "mb-7",
            div { class: "flex justify-between items-center mb-4",
              h2 { class: "text-lg font-bold", {t!("home-todays-activity")} }
              div { class: "h-5 w-5 text-primary", "📅" }
            }

            div { class: "space-y-4",
              ProgressBar {
                title: "".to_string(),
                current_value: activity_progress,
                target_value: daily_activity_goal,
                unit: "min".to_string(),
              }
            }
            br {}
          }

          // * Daily Diet Card
          div { class: "mb-7",
            DailyDietCard {
              daily_calories: daily_calorie_goal,
              preferred_foods: dog().preferred_foods.clone(),
              diet_adjustment: 0, //dog.diet_adjustment.unwrap_or(0),
            }
          }

          // * Treat Tracker
          div { class: "my-2",
            TreatTracker {
              treats_count: 0, // dog.treats_today.unwrap_or(0),
              on_treat_added: handle_add_treat,
              on_treat_removed: handle_remove_treat,
              diet_impact: get_diet_impact_text(),
            }
          }

          // * Nutrition Summary
          div { class: "my-2",
            div { class: "border-none", // Simulating Card
              div { class: "p-0", // Simulating CardContent
                div { class: "flex justify-between items-center mb-4",
                  h2 { class: "text-lg font-bold", {t!("home-nutrition-summary")} }
                  div { class: "h-5 w-5 text-pet-purple", "📅" }
                }

                div { class: "space-y-4",
                  ProgressBar {
                    title: t!("home-daily-calories").to_string(),
                    current_value: calorie_progress,
                    target_value: daily_calorie_goal,
                    unit: "kcal".to_string(),
                  }

                  div { class: "grid grid-cols-3 gap-2 mt-4",
                    div { class: "text-center p-3 bg-white rounded-lg",
                      p { class: "text-sm text-gray-500",
                        {t!("home-protein")}
                      }
                      p { class: "font-bold text-pet-purple",
                        "35%"
                      }
                    }
                    div { class: "text-center p-3 bg-white rounded-lg",
                      p { class: "text-sm text-gray-500", {t!("home-fats")} }
                      p { class: "font-bold text-pet-purple",
                        "25%"
                      }
                    }
                    div { class: "text-center p-3 bg-white rounded-lg",
                      p { class: "text-sm text-gray-500", {t!("home-carbs")} }
                      p { class: "font-bold text-pet-purple",
                        "40%"
                      }
                    }
                  }
                }
              }
            }
          }
        }

        // * Recommendation Card (if any)
        if current_recommendation().is_some() {
          div { class: "px-5 mb-6 fixed z-10 bottom-20",
            DogRecommendationCard {
              recommendation: current_recommendation().unwrap(),
              on_mark_as_seen: handle_mark_recommendation_as_seen,
            }
          }
        }
      }
    };
}
