use dioxus::prelude::*;

use crate::i18n::*;

#[component]
pub fn TreatTracker(
    treats_count: u32,
    on_treat_added: <PERSON><PERSON><PERSON><PERSON><()>,
    on_treat_removed: EventHandler<()>,
    diet_impact: String,
) -> Element {
    rsx! {
        div { class: "border-none",
            div { class: "p-5",
                div { class: "flex justify-between items-center mb-4",
                    p { class: "text-md font-light flex items-center",
                        "Cookie" // Replace with actual icon
                        {t!("treats-treats-today")}
                    }
                    div { class: "flex items-center gap-2",
                        button {
                            class: "h-7 w-7 rounded-full border border-pet-purple text-pet-purple disabled:opacity-50",
                            disabled: treats_count == 0,
                            onclick: move |_| on_treat_removed.call(()),
                            "-"
                        }
                        span { class: "w-8 text-center font-bold", "{treats_count}" }
                        button {
                            class: "h-7 w-7 rounded-full border border-pet-purple text-pet-purple",
                            onclick: move |_| on_treat_added.call(()),
                            "+"
                        }
                    }
                }

                if treats_count > 0 {
                    div { class: "mt-2 text-sm",
                        div { class: "flex justify-between mb-1",
                            span {
                                {t!("treats-diet-impact")}
                                ":"
                            }
                            span { class: if diet_impact.starts_with("-") { "text-green-600" } else { "text-amber-600" },
                                "{diet_impact}"
                            }
                        }
                        p { class: "text-xs text-gray-600",
                            if treats_count > 2 {
                                {t!("treats-reduce-treats")}
                            } else {
                                {t!("treats-within-allowance")}
                            }
                        }
                    }
                }
            }
        }
    }
}
