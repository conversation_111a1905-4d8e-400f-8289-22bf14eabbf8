// use dioxus::prelude::*;

// #[derive(<PERSON><PERSON>, <PERSON>ialEq, <PERSON><PERSON>)]
// pub struct PetAvatarProps {
//     #[props(optional)]
//     pub src:     Option<String>,
//     pub name:    String,
//     #[props(default = "md".to_string())]
//     pub size:    String, // "xs", "sm", "md", "lg"
//     #[props(optional)]
//     pub class:   Option<String>,
//     #[props(optional)]
//     pub onclick: Option<EventHandler<MouseData>>,
// }

// #[component]
// pub fn PetAvatar(props: PetAvatarProps) -> Element {
//     let size_classes = match props.size.as_str() {
//         "xs" => "h-10 w-10",
//         "sm" => "h-12 w-12",
//         "md" => "h-16 w-16",
//         "lg" => "h-24 w-24",
//         _ => "h-16 w-16", // Default to md
//     };

//     let initials: String = props
//         .name
//         .split_whitespace()
//         .filter_map(|s| s.chars().next())
//         .collect::<String>()
//         .to_uppercase();

//     rsx!(
//       div {
//         class: "{size_classes} rounded-full shadow-md hover:shadow-lg transition-shadow duration-200
// {props.class.as_deref().unwrap_or(\"\")}",         onclick: move |evt| {
//             if let Some(handler) = &props.onclick {
//                 handler.call(evt);
//             }
//         },
//         if let Some(image_src) = &props.src {
//           img {
//             src: "{image_src}",
//             alt: "{props.name}",
//             class: "w-full h-full object-cover rounded-full",
//           }
//         } else {
//           div { class: "w-full h-full flex items-center justify-center rounded-full pet-gradient
// text-white",             "{initials}"
//           }
//         }
//       }
//     )
// }
