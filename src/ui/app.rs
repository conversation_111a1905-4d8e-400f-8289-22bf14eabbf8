use dioxus::{
    logger::tracing::info,
    prelude::*,
};
use dioxus_radio::prelude::*;

// use uuid::Uuid;
use crate::{
    // hooks::{
    //     data::{
    //         use_activity_data::use_activity_data,
    //         use_meal_data::use_meal_data,
    //         use_recommendation_data::use_recommendation_data,
    //         use_weight_log_data::use_weight_log_data,
    //     },
    //     use_auth::{
    //         AuthProvider,
    //         use_auth,
    //     },
    //     use_language::{
    //         LanguageProvider,
    //         use_language,
    //     },
    //     use_walk_tracker::{
    //         WalkTrackerProvider,
    //         use_walk_tracker,
    //     },
    // },
    i18n::*,
    models::*,
    ui::{
        layout::BottomNavbar,
        pages::{
            // ActivityHistory,
            // AddDog,
            // DogProfile,
            Home,
            NotFound,
            Onboarding,
            // Results,
            Settings,
            WeightHistory,
        },
    },
};


const APP_NAME: &str = "My Dog in Fit";
const FAVICON: Asset = asset!("/assets/favicon.ico");
// const MAIN_CSS: Asset = asset!("/assets/styling/main.css");
#[cfg(debug_assertions)]
const TAILWIND_CSS: Asset = asset!("/assets/tailwind.css", CssAssetOptions::new().with_minify(false));
#[cfg(not(debug_assertions))]
const TAILWIND_CSS: Asset = asset!("/assets/tailwind.css", CssAssetOptions::new().with_minify(true));


#[derive(Routable, Clone, PartialEq)]
pub enum Route {
    #[layout(WithNavbar)]
    #[route("/")]
    Home {},
    #[route("/settings")]
    Settings {},
    #[end_layout]
    // #[route("/activities/history")]
    // ActivityHistory {},
    // #[route("/add-dog")]
    // AddDog {},
    // #[route("/ai")]
    // AIChat {},
    // #[route("/dog/:id")]
    // DogProfile { id: String },
    #[route("/onboarding")]
    Onboarding {},
    // #[route("/results")]
    // ResultsPage {},
    #[route("/weight-history")]
    WeightHistory {},
    #[route("/*path")]
    NotFound { path: String },
}


#[component]
pub fn App() -> Element {
    info!("App()");
    use_init_radio_station::<AppState, AppEvent>(AppState::default);

    use_context_provider(|| AppState::default());
    // let state = use_context_provider(|| {
    //     let mut dogs = get_dogs();
    //     let mut selected_dog_id = get_selected_pet_id();

    //     if !dogs.iter().any(|p| p.id == selected_dog_id) {
    //         selected_dog_id = dogs.first().map(|p| p.id.clone()).unwrap_or_default();
    //     }

    //     AppState {
    //         dogs,
    //         theme: get_item("theme").unwrap_or(Theme::Light),
    //         treats_count: get_item("treats_count").unwrap_or(0),
    //         preferred_foods: get_item("preferred_foods").unwrap_or_default(),
    //         selected_dog_id,
    //         language: get_item("language").unwrap_or_else(|| "en".to_string()),
    //         debug_mode: get_item("debug_mode").unwrap_or(false),
    //     }
    // });

    // use_effect(move || {
    //     save_item(APP_STATE_KEY, &*state.read());
    // });

    // // Save pets whenever state.pets changes
    // use_effect(move || {
    //     save_pets(&pets);
    //     async {}
    // });

    // // Save selected_pet_id whenever it changes
    // use_effect(move || {
    //     save_selected_pet_id(&id);
    //     async {}
    // });

    // // Save theme whenever state.theme changes
    // use_effect(move || {
    //     save_item("theme", theme);
    //     async {}
    // });

    // // Save treats_count whenever state.treats_count changes
    // use_effect(move || {
    //     save_item("treats_count", count);
    //     async {}
    // });

    // // Save preferred_foods whenever state.preferred_foods changes
    // use_effect(move || {
    //     save_item("preferred_foods", foods);
    //     async {}
    // });

    // // Save language whenever state.language changes
    // use_effect(move || {
    //     save_item("language", lang);
    //     async {}
    // });

    // // Save debug_mode whenever state.debug_mode changes
    // use_effect(move || {
    //     save_item("debug_mode", mode);
    //     async {}
    // });

    rsx! {
        document::Meta {
            name: "viewport",
            content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover",
                // content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content",
        }
        document::Meta { name: "mobile-web-app-capable", content: "yes" }
        document::Meta { name: "apple-mobile-web-app-capable", content: "yes" }
        document::Meta {
            name: "apple-mobile-web-app-status-bar-style",
            content: "black-translucent",
        }
        document::Meta {
            name: "format-detection",
            content: "telephone=no, date=no, address=no, email=no",
        }
        document::Meta { name: "msapplication-TileColor", content: "#ffffff" }
        document::Meta {
            name: "msapplication-TileImage",
            content: "/icons/icon-192x192.png",
        }
        document::Meta { name: "msapplication-config", content: "/icons/browserconfig.xml" }
        document::Meta { name: "apple-touch-fullscreen", content: "yes" }
        document::Meta { name: "apple-mobile-web-app-title", content: "{APP_NAME}" }
        document::Meta { name: "application-name", content: "{APP_NAME}" }
        document::Meta {
            name: "description",
            content: "Track your dog's fitness and health",
        }
        document::Meta {
            name: "theme-color",
            // content: "hsl(var(--color-primary))",
            content: "#f7f8ff",
        }
        document::Meta { name: "color-scheme", content: "light dark" }

        document::Link { rel: "icon", href: FAVICON }
        // document::Stylesheet { rel: "stylesheet", href: MAIN_CSS }
        document::Stylesheet { rel: "stylesheet", href: TAILWIND_CSS }

        LocalizedApp {}
    }
}

#[component]
pub fn LocalizedApp() -> Element {
    info!("LocalizedApp()");

    rsx! {
        // AuthProvider {
        // WalkTrackerProvider {
        I18nProvider { AppLayout {} }
    }
}

#[component]
pub fn AppLayout() -> Element {
    info!("AppLayout()");
    rsx! {
        div { class: "mx-auto absolute top-0 bottom-0 left-0 right-0",
            // style: "padding-top: calc(env(safe-area-inset-top, 0px) + 8px); padding-bottom: env(safe-area-inset-bottom); padding-left: env(safe-area-inset-left); padding-right: env(safe-area-inset-right);",
            // style: "padding-top: 24px; padding-bottom: env(safe-area-inset-bottom);",
            // class: "{getPageGradientClass()}",
            // Toaster {}
            // Sonner {}
            Router::<Route> {}
                // OfflineIndicator {}
        // BottomNavbar {}
        }
    }
}

#[component]
pub fn WithNavbar() -> Element {
    info!("WithNavbar()");
    rsx! {
        Outlet::<Route> {}
        BottomNavbar {}
    }
}
