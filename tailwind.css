@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@import 'tailwindcss';
/* @config "../tailwind.config.ts"; */


@custom-variant colored {
  &:where([data-theme='colored'] *) {
    @slot;
  }
}

@theme {
  /* --color-blue: hsl(221.9, 84.5%, 56.9%); */
  --color-blue: hsl(198.24, 100%, 44.51%);
  --color-calm-blue: hsl(194.5, 47.3%, 39.4%);
  --color-dark-blue: hsl(199.4, 39.7%, 35.1%);
  --color-light-blue: hsl(190.38, 63.41%, 83.92%);
  /* --color-purple: #9b87f5; */
  /* --color-purple: hsl(282.8, 81.1%, 31.2%); */
  --color-purple: hsl(250.8, 50%, 60.78%);
  --color-light-purple: hsl(252.73, 100%, 93.53%);
  /* --color-darkPurple: #6E59A5; */
  /* --color-dark-purple: hsl(284.19, 83.23%, 30.39%); */
  --color-dark-purple: hsl(292.3, 50%, 27.5%);
  /* --color-green: #F2FCE2; */
  --color-green: hsl(105, 57.14%, 50.59%);
  /* --color-green: #92a06b; */
  /* --color-green: #46b57b; */
  --color-light-green: hsl(94.7, 54.2%, 83.7%);
  /* #d2ecbf */
  --color-green-tea: hsl(57.9, 33.6%, 49.6%);
  --color-green-tea2: hsl(56.7, 97.8%, 36.5%);
  /* #b8ae02 */
  --color-green-tea-light: hsl(101.1, 25.2%, 72.7%);
  --color-green-tea-dark: hsl(101.1, 25.2%, 52.7%);
  /* #7ba568 */
  --color-green-tea-fresh: hsl(56.5, 64.1%, 57.5%);
  /* #d8d04d */
  --color-green-tea-calm: hsl(71.8, 32.2%, 53.7%);
  --color-acid-green: hsl(78.3, 98.24%, 44.51%);
  /* --color-yellow: #FEF7CD; */
  /* --color-yellow: #f4c48d; */
  --color-yellow: hsl(54.39, 98.17%, 78.63%);
  --color-light-yellow: #fef493;
  /* --color-orange: #FEC6A1; */
  --color-orange: hsl(36.36, 98.02%, 60.39%);
  --color-light-orange: #fec772;
  /* --color-pink: #FFDEE2; */
  /* --color-pink: #e93a6d; */
  /* --color-pink: #de6a93; */
  --color-pink: hsl(327.4, 67.2%, 62.94%);
  --color-navy: hsl(223.33, 25.71%, 13.73%);
  --color-teal: hsl(175.74, 61.57%, 44.9%);
  --color-dark-teal: #23bcab;
  --color-red: hsl(0.74, 84.38%, 62.35%);
  /* --color-red: #fa6a6e; */
  /* --color-brown: hsl(30.31, 45.12%, 57.84%); */
  /* --color-brown: hsl(30, 39%, 47%); */
  /* --color-brown: hsl(23.6, 66.7%, 58.8%); */
  /* --color-brown: hsl(19.3, 36.6%, 51.8%); */
  /* --color-brown: hsl(30, 84%, 38%); */
  --color-brown: hsl(30, 70%, 38%);

  --color-pet-background: #f7f8ff;
  --color-pet-blue: #009ee3;
  --color-pet-lightBlue: #bce7f0;
  --color-pet-purple: #7b69cd;
  --color-pet-lightPurple: #E5DEFF;
  --color-pet-darkPurple: #6c0d8e;
  --color-pet-green: #46b57b;
  --color-pet-lightGreen: #d2ecbf;
  --color-pet-acidGreen: #9de102;
  --color-pet-yellow: #f4cc50;
  --color-pet-lightYellow: #fef493;
  --color-pet-orange: #fdaf37;
  --color-pet-lightOrange: #fec772;
  --color-pet-pink: #e061a6;
  --color-pet-navy: #1A1F2C;
  --color-pet-teal: #2cb9af;
  --color-pet-darkTeal: var(--color-dark-teal);
  --color-pet-red: #fa6a6e;
  --color-pet-brown: #c49463;
  --color-pet-glow: #b69eff;
  --color-pet-activity: #FF7D32;
  --color-pet-weight: #346cee;
  --color-pet-food: #23bcab;
  --color-pet-health: #50C878;
  --color-pet-time: #e93a6d;


  /* --color-background: hsl(260, 30%, 98%); */
  /* --color-background: hsl(260, 30%, 100%); */
  /* --color-background: white; */
  /* --color-background: #f7f8ff; */
  /* --color-background: hsl(5.71, 28%, 95.1%); */
  /* --color-background: #fef3ef; */
  --color-background: var(--color-light-green);
  /* --color-background: "var(--green-tea-light)"; */
  --color-background-darker: var(--color-green-tea-calm);

  --color-foreground: hsl(222.2, 84%, 4.9%);

  --color-card: hsl(0, 0%, 100%);
  --color-card-foreground: hsl(222.2, 84%, 4.9%);

  --color-popover: hsl(0, 0%, 100%);
  --color-popover-foreground: hsl(222.2, 84%, 4.9%);

  /* --color-primary: hsl(260, 70%, 75%); */
  /* --color-primary: hsl(175.74, 61.57%, 44.9%); */
  /* --color-primary-foreground: hsl(0, 0%, 0%); */
  /* --color-primary-foreground: hsl(175.74, 61.57%, 44.9%); */
  --color-primary: var(--color-green-tea2);
  --color-primary-foreground: var(--color-brown);

  /* --color-secondary: hsl(260, 40%, 96.1%); */
  /* --color-secondary-foreground: hsl(222.2, 47.4%, 11.2%); */
  --color-secondary: var(--color-calm-blue);
  --color-secondary-background: var(--color-dark-blue);
  --color-secondary-foreground: var(--color-blue);

  --color-muted: hsl(210, 40%, 96.1%);
  --color-muted-foreground: hsl(215.4, 16.3%, 46.9%);

  --color-accent: hsl(260, 40%, 96.1%);
  --color-accent-foreground: hsl(222.2, 47.4%, 11.2%);

  --color-destructive: hsl(0, 84.2%, 60.2%);
  --color-destructive-foreground: hsl(210, 40%, 98%);

  --color-border: hsl(214.3, 31.8%, 91.4%);
  --color-input: hsl(214.3, 31.8%, 91.4%);
  --color-ring: hsl(260, 70%, 75%);

  --color-sidebar-background: hsl(0, 0%, 98%);
  --color-sidebar-foreground: hsl(240, 5.3%, 26.1%);
  --color-sidebar-primary: hsl(260, 70%, 75%);
  --color-sidebar-primary-foreground: hsl(0, 0%, 98%);
  --color-sidebar-accent: hsl(240, 4.8%, 95.9%);
  --color-sidebar-accent-foreground: hsl(240, 5.9%, 10%);
  --color-sidebar-border: hsl(220, 13%, 91%);
  --color-sidebar-ring: hsl(217.2, 91.2%, 59.8%);

}

@layer base {
  * {
    /* @apply border; */
    @apply border-border;
    font-family: system-ui, Inter, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  /* iOS & macOS system font */
  @supports (-webkit-touch-callout: none) {
    * {
      font-family: -apple-system, BlinkMacSystemFont, system-ui, Inter, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
  }

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    /* border-color: var(--color-gray-200, currentColor);   */
    border-color: transparent;
    border-style: none;
    border-width: 0;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply text-foreground;
    background-color: color-mix(in srgb, var(--color-background), var(--color-primary) 20%);
    position: relative;
  }

  html,
  body {
    @apply w-full h-full;
    margin: 0;
    padding: 0;
  }

  #main {
    @apply w-full h-full;
  }

  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }

  dialog {
    margin: auto;
  }

  :root {
    --radius: 1rem;
    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
  }

  .dark {
    --background: hsl(235, 24%, 10%);
    --foreground: hsl(0, 0%, 98%);

    --card: hsl(235, 21%, 11%);
    --card-foreground: hsl(0, 0%, 98%);

    --popover: hsl(235, 21%, 11%);
    --popover-foreground: hsl(0, 0%, 98%);

    --primary: hsl(260, 70%, 75%);
    --primary-foreground: hsl(240, 5.9%, 10%);

    --secondary: hsl(240, 8%, 18%);
    --secondary-foreground: hsl(0, 0%, 98%);

    --muted: hsl(240, 8%, 18%);
    --muted-foreground: hsl(240, 5%, 74%);

    --accent: hsl(240, 8%, 24%);
    --accent-foreground: hsl(0, 0%, 98%);

    --destructive: hsl(0, 62.8%, 40.6%);
    --destructive-foreground: hsl(0, 0%, 98%);

    --border: hsl(240, 8%, 22%);
    --input: hsl(240, 8%, 22%);
    --ring: hsl(260, 65%, 65%);

    --sidebar-background: hsl(235, 24%, 9%);
    --sidebar-foreground: hsl(0, 0%, 98%);
    --sidebar-primary: hsl(260, 70%, 75%);
    --sidebar-primary-foreground: hsl(240, 5.9%, 10%);
    --sidebar-accent: hsl(240, 8%, 18%);
    --sidebar-accent-foreground: hsl(0, 0%, 98%);
    --sidebar-border: hsl(240, 8%, 22%);
    --sidebar-ring: hsl(217.2, 91.2%, 59.8%);
  }

  .colored {
    --background: hsl(260, 30%, 98%);
    --foreground: hsl(0, 0%, 100%);

    --card: hsl(0, 0%, 100%);
    --card-foreground: hsl(0, 0%, 100%);

    --popover: hsl(260, 40%, 50%);
    --popover-foreground: hsl(0, 0%, 100%);

    --primary: hsl(0, 0%, 100%);
    --primary-foreground: hsl(260, 40%, 50%);

    --secondary: hsl(260, 40%, 60%);
    --secondary-foreground: hsl(0, 0%, 100%);

    --muted: hsl(260, 40%, 60%);
    --muted-foreground: hsl(0, 0%, 90%);

    --accent: hsl(260, 40%, 70%);
    --accent-foreground: hsl(0, 0%, 100%);

    --destructive: hsl(0, 62.8%, 60.6%);
    --destructive-foreground: hsl(0, 0%, 100%);

    --border: hsl(260, 40%, 70%);
    --input: hsl(260, 40%, 70%);
    --ring: hsl(0, 0%, 100%);

    --sidebar-background: hsl(260, 40%, 50%);
    --sidebar-foreground: hsl(0, 0%, 100%);
    --sidebar-primary: hsl(0, 0%, 100%);
    --sidebar-primary-foreground: hsl(260, 40%, 50%);
    --sidebar-accent: hsl(260, 40%, 60%);
    --sidebar-accent-foreground: hsl(0, 0%, 100%);
    --sidebar-border: hsl(260, 40%, 70%);
    --sidebar-ring: hsl(0, 0%, 100%);
  }
}

@utility pet-gradient {
  @apply bg-linear-to-br from-primary to-primary;
  /* @apply bg-gradient-to-br from-secondary/50 to-secondary; */
  /* @apply bg-gradient-to-br from-pet-teal/70 to-pet-teal; */
  /* @apply bg-gradient-to-br from-pet-pink/70 to-pet-pink; */
  /* @apply bg-gradient-to-br from-pet-orange/70 to-pet-orange; */
  /* @apply bg-gradient-to-br from-pet-blue/70 to-pet-blue; */
}

@utility pet-card-gradient-teal {
  @apply bg-linear-to-br from-pet-teal/40 to-pet-teal/90;
}

@utility pet-card-gradient-purple {
  @apply bg-linear-to-br from-pet-purple/30 to-pet-purple/70;
}

@utility pet-card-gradient-pink {
  @apply bg-linear-to-br from-pet-pink/95 to-pet-pink/65;
}

@utility pet-card-gradient-blue {
  @apply bg-linear-to-br from-pet-blue/0 to-pet-blue/95;
}

@utility pet-card-gradient-orange {
  @apply bg-linear-to-br from-pet-orange/50 to-pet-orange/95;
}

@utility pet-card-gradient-lightPurple {
  @apply bg-linear-to-br from-pet-lightPurple/50 to-pet-lightPurple;
}

@utility pet-card-gradient-yellow {
  @apply bg-linear-to-br from-pet-yellow/95 to-pet-yellow/55;
}

/* @utility pet-card-gradient-teal {
  @apply bg-gradient-to-br from-pet-teal/60 to-pet-teal;
}
@utility pet-card-gradient-purple {
  @apply bg-gradient-to-br from-pet-purple/40 to-pet-purple/80;
}
@utility pet-card-gradient-pink {
  @apply bg-gradient-to-br from-pet-pink/60 to-pet-pink;
}
@utility pet-card-gradient-blue {
  @apply bg-gradient-to-br from-pet-blue/50 to-pet-blue;
}
@utility pet-card-gradient-orange {
  @apply bg-gradient-to-br from-pet-orange/60 to-pet-orange;
}
@utility pet-card-gradient-lightPurple {
  @apply bg-gradient-to-br from-pet-lightPurple/50 to-pet-lightPurple;
} */

@utility pet-card {
  @apply bg-transparent backdrop-blur-xs border-0 shadow-md;
}

@utility glass {
  @apply backdrop-blur-xl bg-white/20 border-0 shadow-md dark:bg-black/20 dark:border-white/10 colored:bg-white/20 colored:border-white/10;
}

@utility glass-card {
  @apply backdrop-blur-lg bg-white/20 border-0 shadow-md rounded-xl dark:bg-black/20 dark:border-white/10 colored:bg-white/20 colored:border-white/10;
}

@utility neo-blur {
  @apply backdrop-blur-2xl bg-white/5 border-0 dark:bg-black/40 colored:bg-white/10;
}

@utility text-gradient {
  /* @apply bg-gradient-to-br from-pet-purple via-pet-darkPurple to-pet-purple/70 bg-clip-text text-transparent; */
  @apply bg-linear-to-br from-pet-teal via-pet-darkTeal to-pet-teal/70 bg-clip-text text-transparent;
}

@utility hover-scale {
  @apply transition-transform duration-200 hover:scale-105;
}

@utility pulse {
  @apply animate-pulse;
}

/* Full-width header gradients for mobile */
@utility bg-gradient-to-b {
  @apply w-full;
}

/* Colored theme specific page gradients */
@utility colored-page-gradient-1 {
  @apply bg-linear-to-br from-purple-500/90 via-violet-500/80 to-indigo-500/70;
}

@utility colored-page-gradient-2 {
  @apply bg-linear-to-br from-cyan-500/90 via-blue-500/80 to-purple-500/70;
}

@utility colored-page-gradient-3 {
  @apply bg-linear-to-br from-rose-400/90 via-fuchsia-500/80 to-indigo-500/70;
}

@utility colored-page-gradient-4 {
  @apply bg-linear-to-br from-amber-400/90 via-red-500/80 to-purple-500/70;
}

@utility colored-page-gradient-5 {
  @apply bg-linear-to-br from-emerald-400/90 via-teal-500/80 to-blue-500/70;
}

/* Icon specific colors */
@utility icon-activity {
  @apply text-pet-activity;
}

@utility icon-dog {
  @apply text-black;
}

@utility icon-weight {
  @apply text-pet-weight;
}

@utility icon-food {
  @apply text-pet-food;
}

@utility icon-health {
  @apply text-pet-health;
}

/* Icon circles for colored theme */
@utility icon-circle {
  @apply rounded-full p-3 flex items-center justify-center;
}

/* * https://tailwindcss.com/docs/adding-custom-styles#functional-utilities */
@utility backdrop-blur-* {
  --tw-backdrop-blur: blur(--value(--blur-*));
  -webkit-backdrop-filter: var(--tw-backdrop-blur);
  backdrop-filter: var(--tw-backdrop-blur);
}


@layer utilities {
  .border-radius {
    --lg: var(--radius);
    --md: calc(var(--radius) - 2px);
    --sm: calc(var(--radius) - 4px);
    --2xl: 1rem;
    --3xl: 1.5rem;
  }

  .keyframes {
    --accordion-down: {
      from: {
        height: 0;
      }

      to: {
        height: var(--radix-accordion-content-height);
      }
    }

    --accordion-up: {
      from: {
        height: var(--radix-accordion-content-height);
      }

      to: {
        height: 0;
      }
    }

    --bounce-light: {
      "0%, 100%": {
        transform: translateY(-3%);
        animationTimingFunction: cubic-bezier(0.8, 0, 1, 1);
      }

      "50%": {
        transform: translateY(0);
        animationTimingFunction: cubic-bezier(0, 0, 0.2, 1);
      }
    }

    --fade-in: {
      "0%": {
        opacity: 0;
        transform: translateY(10px);
      }

      "100%": {
        opacity: 1;
        transform: translateY(0);
      }
    }

    --scale-in: {
      "0%": {
        opacity: 0;
        transform: scale(0.95);
      }

      "100%": {
        opacity: 1;
        transform: scale(1);
      }
    }

    --glow: {
      "0%, 100%": {
        boxShadow: 0 0 5px 2px rgba(182, 158, 255, 0.2);
      }

      "50%": {
        boxShadow: 0 0 15px 5px rgba(182, 158, 255, 0.4);
      }
    }
  }

  .animation {
    --accordion-down: accordion-down 0.2s ease-out;
    --accordion-up: accordion-up 0.2s ease-out;
    --bounce-light: bounce-light 2s infinite;
    --fade-in: fade-in 0.3s ease-out forwards;
    --scale-in: scale-in 0.2s ease-out forwards;
    --glow: glow 3s ease-in-out infinite;
  }

  .box-shadow {
    --glow-sm: 0 0 5px 2px rgba(182, 158, 255, 0.2);
    --glow-md: 0 0 15px 5px rgba(182, 158, 255, 0.3);
    --glow-lg: 0 0 25px 10px rgba(182, 158, 255, 0.4);
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration: none;
    }

    .hover\:bg-accent {
      &:hover {
        background-color: unset;
      }
    }
  }
}

@utility colored-icon-circle-activity {
  @apply bg-pet-activity/30;
}

@utility colored-icon-circle-weight {
  @apply bg-pet-weight/30;
}

@utility colored-icon-circle-food {
  @apply bg-pet-food/30;
}

@utility colored-icon-circle-health {
  @apply bg-pet-health/30;
}

/* Full-width card sections */
@utility full-width-card {
  @apply px-0 w-full bg-transparent border-0 shadow-none;
}

@utility page-content {
  @apply px-5 space-y-4;
}

@utility page {
  @apply pb-20;
}

@utility page-header {
  @apply sticky top-0 z-10 mb-3 pb-2 backdrop-blur-3xl page-content;
}

@utility section {
  @apply mb-4 pt-4 pb-1 page-content;
}

@utility section-colored {
  @apply section rounded-3xl shadow-glow-sm bg-background;
}

@utility card {
  @apply rounded-lg border bg-card text-card-foreground shadow-sm page-content pt-4 pb-6;
}

@utility header {
  @apply flex flex-col space-y-1.5 mb-4;
}

@utility title {
  @apply text-2xl font-semibold leading-none tracking-tight;
}

@utility description {
  @apply text-sm text-muted-foreground;
}

@utility content {
  @apply flex flex-col space-y-4 p-0;
}

@utility footer {
  @apply flex items-center pt-2;
}

@utility col {
  @apply flex flex-col items-center justify-center;
}

@utility row {
  @apply flex flex-row items-center justify-between
}

@utility shadow {
  --glow-sm: 0 0 5px 2px rgba(182, 158, 255, 0.2);
  --glow-md: 0 0 15px 5px rgba(182, 158, 255, 0.3);
  --glow-lg: 0 0 25px 10px rgba(182, 158, 255, 0.4);
}

@utility shadow-glow-sm {
  box-shadow: 0 0 5px 2px rgba(182, 158, 255, 0.2);
}

@utility shadow-glow-md {
  box-shadow: 0 0 15px 5px rgba(182, 158, 255, 0.3);
}

@utility shadow-glow-lg {
  box-shadow: 0 0 25px 10px rgba(182, 158, 255, 0.4);
}

/* Full-screen container adjustments */
#root {
  max-width: 100%;
  margin: 0;
  padding: 0;
}

/* Remove padding from app container for mobile view */
@media (max-width: 768px) {
  .max-w-md {
    max-width: 100%;
    padding: 0;
  }

  /* Remove horizontal padding from gradient headers */
  .bg-gradient-to-b {
    padding-left: 0;
    padding-right: 0;
    /* width: 100vw; */
    margin-left: 0;
    margin-right: 0;
  }

  /* Adjust padding within header content */
  .bg-gradient-to-b>div {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  /* Full width sections */
  .section-full-width {
    margin-left: -1rem;
    margin-right: -1rem;
    width: calc(100% + 2rem);
  }
}

/* Animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.scale-in {
  animation: scaleIn 0.2s ease-out forwards;
}

/* Rounded corners for dropdowns */
.radix-dropdown-menu-content,
.radix-popover-content,
.radix-menu-content,
.radix-dialog-content,
.radix-popover {
  @apply rounded-xl overflow-hidden;
}

/* Shadow effects */
.shadow-soft {
  box-shadow: 0px 8px 24px rgba(149, 157, 165, 0.1);
}

.shadow-hover {
  transition: box-shadow 0.3s ease;
}

.shadow-hover:hover {
  box-shadow: 0px 8px 24px rgba(149, 157, 165, 0.2);
}


*:focus {
  outline: none !important;
  outline-style: none !important;
  box-shadow: none !important;
}

/* Fix for the DayPicker component not being full width on mobile */
.rdp-month .rdp-month {
  visibility: hidden;
  display: none;
}

.rdp-day.rdp-outside {
  color: #aaa;
}

.rdp-day.rdp-outside.rdp-selected {
  color: #fff;
}

.rdp-nav {
  padding-bottom: 0.4rem;
}
