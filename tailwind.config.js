/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: "all",
  content: [
    // "./pages/**/*.{ts,tsx}",
    // "./components/**/*.{ts,tsx}",
    // "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "./src/**/*.{rs,html,css}",
    // "./dist/**/*.html",
    // "./target/**/*.html",
  ],
  // content: ["./src/**/*.{rs,html,css}"],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        // background: "hsl(var(--background))",
        background: "hsl(var(--light-green))",
        backgroundDarker: "hsl(var(--green-tea-dark))",
        // background: "hsl(var(--green-tea-light))",
        // background: "hsl(var(--green-tea-fresh))",
        foreground: "hsl(var(--foreground))",
        primary: {
          // DEFAULT: 'hsl(var(--primary))',
          // foreground: 'hsl(var(--primary-foreground))',

          // DEFAULT: 'hsl(var(--blue))',
          // DEFAULT: 'hsl(var(--brown))', // <--
          // DEFAULT: 'hsl(var(--green))',
          // DEFAULT: "hsl(var(--green-tea))",
          DEFAULT: "hsl(var(--green-tea2))", // <--
          // DEFAULT: "hsl(var(--green-tea-light))",
          // DEFAULT: "hsl(var(--green-tea-dark))",
          // DEFAULT: "hsl(var(--green-tea-fresh))",
          // DEFAULT: "hsl(var(--green-tea-calm))",
          // DEFAULT: 'hsl(var(--orange))',
          // DEFAULT: 'hsl(var(--purple))',
          // DEFAULT: 'hsl(var(--red))',
          // DEFAULT: 'hsl(var(--teal))',
          // DEFAULT: 'hsl(var(--yellow))',

          foreground: "hsl(var(--brown))",
        },
        secondary: {
          // DEFAULT: 'hsl(var(--secondary))',
          // foreground: 'hsl(var(--secondary-foreground))'

          // DEFAULT: "hsl(var(--blue))",
          DEFAULT: "hsl(var(--calm-blue))",
          // DEFAULT: "hsl(var(--dark-blue))",
          // DEFAULT: 'hsl(var(--brown))',
          // DEFAULT: 'hsl(var(--green))',
          // DEFAULT: 'hsl(var(--orange))',
          // DEFAULT: 'hsl(var(--purple))',
          // DEFAULT: 'hsl(var(--red))',
          // DEFAULT: 'hsl(var(--teal))',
          // DEFAULT: 'hsl(var(--yellow))',

          background: "hsl(var(--dark-blue))",
          foreground: "hsl(var(--blue))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        pet: {
          // background: '#f6f2ef',
          background: "#f7f8ff",
          // blue: '#346cee',
          blue: "#009ee3",
          lightBlue: "#bce7f0",
          // purple: '#9b87f5',
          // purple: '#6b0f90',
          purple: "#7b69cd",
          lightPurple: "#E5DEFF",
          // darkPurple: '#6E59A5',
          darkPurple: "#6c0d8e",
          // green: '#F2FCE2',
          // green: '#5dc939',
          // green: '#92a06b',
          green: "#46b57b",
          lightGreen: "#d2ecbf",
          acidGreen: "#9de102",
          // yellow: '#FEF7CD',
          // yellow: '#f4c48d',
          yellow: "#f4cc50",
          lightYellow: "#fef493",
          // orange: '#FEC6A1',
          orange: "#fdaf37",
          lightOrange: "#fec772",
          // pink: '#FFDEE2',
          // pink: '#e93a6d',
          // pink: '#de6a93',
          pink: "#e061a6",
          navy: "#1A1F2C",
          // teal: '#23bcab',
          teal: "#2cb9af",
          // red: '#f0504e',
          red: "#fa6a6e",
          brown: "#c49463",

          glow: "#b69eff",
          // Updated icon colors based on screenshots
          // activity: '#FF7D32', // Bright orange
          activity: "#FF7D32", // Bright orange
          // weight: '#2E86FB', // Bright blue
          weight: "#346cee", // Bright blue
          // food: '#E950FF', // Vibrant purple/pink
          food: "#23bcab", // Vibrant purple/pink
          // health: '#50C878', // Emerald green
          health: "#50C878", // Emerald green
          // time: '#FF4B4B', // Red for time icons
          time: "#e93a6d", // Red for time icons
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        "2xl": "1rem",
        "3xl": "1.5rem",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "bounce-light": {
          "0%, 100%": {
            transform: "translateY(-3%)",
            animationTimingFunction: "cubic-bezier(0.8, 0, 1, 1)",
          },
          "50%": {
            transform: "translateY(0)",
            animationTimingFunction: "cubic-bezier(0, 0, 0.2, 1)",
          },
        },
        "fade-in": {
          "0%": {
            opacity: "0",
            transform: "translateY(10px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
        "scale-in": {
          "0%": {
            opacity: "0",
            transform: "scale(0.95)",
          },
          "100%": {
            opacity: "1",
            transform: "scale(1)",
          },
        },
        glow: {
          "0%, 100%": {
            boxShadow: "0 0 5px 2px rgba(182, 158, 255, 0.2)",
          },
          "50%": {
            boxShadow: "0 0 15px 5px rgba(182, 158, 255, 0.4)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "bounce-light": "bounce-light 2s infinite",
        "fade-in": "fade-in 0.3s ease-out forwards",
        "scale-in": "scale-in 0.2s ease-out forwards",
        glow: "glow 3s ease-in-out infinite",
      },
      boxShadow: {
        "glow-sm": "0 0 5px 2px rgba(182, 158, 255, 0.2)",
        "glow-md": "0 0 15px 5px rgba(182, 158, 255, 0.3)",
        "glow-lg": "0 0 25px 10px rgba(182, 158, 255, 0.4)",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    function({addVariant}) {
      // Add a custom variant for the "colored" theme
      addVariant("colored", ".colored &");
    },
  ],
  // daisyui: {
  //   themes: ["material"],
  // },
};
